package com.pugwoo.branch.database.vo;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableColumnDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

@Data
public class DatabaseStdWideTableColumnVO extends DatabaseStdWideTableColumnDO {

    @RelatedColumn(localColumn = "database_id", remoteColumn = "id")
    private DatabaseDO databaseDO;

    /**vm用到*/
    public String getDatabase() {
        return databaseDO == null ? "" : databaseDO.getName();
    }

}
